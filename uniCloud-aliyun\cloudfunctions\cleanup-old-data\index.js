"use strict";

const OSS = require("ali-oss");
const createConfig = require("uni-config-center");

/**
 * 数据清理云函数 - 定时清理超过3天的旧数据
 *
 * 功能说明：
 * 1. 清理创建时间超过3天的任务记录
 * 2. 清理对应的OSS存储文件（原始视频、音频、字幕、最终视频）
 * 3. 确保数据库记录和OSS文件的一致性清理
 * 4. 记录清理统计信息和审计日志
 * 5. 支持基于openId的精准清理特定用户数据
 *
 * 触发方式：
 * - 定时触发器：每天凌晨2点执行（cron: 0 2 * * *）
 * - 手动调用：可传入参数进行测试或手动清理
 *
 * @param {Object} event
 * @param {boolean} event.dryRun - 是否为试运行模式（只查询不删除，默认false）
 * @param {number} event.daysThreshold - 清理天数阈值（默认3天）
 * @param {number} event.batchSize - 批量处理大小（默认50）
 * @param {boolean} event.skipOssCleanup - 是否跳过OSS文件清理（默认false）
 * @param {string} event.openId - 可选，指定用户的openId，如果提供则只清理该用户的数据
 * @returns {Object} 清理结果统计
 */
exports.main = async (event, context) => {
  try {
    const {
      dryRun = false,
      daysThreshold = 3,
      batchSize = 50,
      skipOssCleanup = false,
      openId = null
    } = event;

    const { CLIENTIP } = context;

    console.log('cleanup-old-data 云函数被调用，参数：', {
      dryRun,
      daysThreshold,
      batchSize,
      skipOssCleanup,
      openId,
      clientIP: CLIENTIP,
      triggerTime: new Date().toISOString()
    });

    // ==================== 参数验证 ====================
    if (daysThreshold < 1 || daysThreshold > 365) {
      console.error('参数验证失败：daysThreshold必须在1-365之间');
      return {
        code: 400,
        message: 'daysThreshold参数必须在1-365之间'
      };
    }

    if (batchSize < 1 || batchSize > 200) {
      console.error('参数验证失败：batchSize必须在1-200之间');
      return {
        code: 400,
        message: 'batchSize参数必须在1-200之间'
      };
    }

    // 验证openId参数（如果提供）
    if (openId !== null && (typeof openId !== 'string' || openId.trim() === '')) {
      console.error('参数验证失败：openId必须是非空字符串');
      return {
        code: 400,
        message: 'openId参数必须是非空字符串'
      };
    }

    // ==================== 计算清理时间阈值 ====================
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysThreshold);

    if (openId) {
      console.log(`开始清理用户 ${openId} 的${daysThreshold}天前的数据，截止时间：${cutoffDate.toISOString()}`);
    } else {
      console.log(`开始清理${daysThreshold}天前的数据，截止时间：${cutoffDate.toISOString()}`);
    }

    // ==================== 数据库连接 ====================
    const db = uniCloud.database();
    const tasksCollection = db.collection('tasks');

    // ==================== 通过openId查找userId ====================
    let targetUserId = null;
    if (openId) {
      console.log(`通过openId查找对应的userId: ${openId}`);
      targetUserId = await getUserIdByOpenId(db, openId);

      if (!targetUserId) {
        console.log(`未找到openId ${openId} 对应的用户`);
        return {
          code: 404,
          message: '未找到指定的用户',
          data: {
            openId: openId,
            cleanupDate: cutoffDate.toISOString(),
            tasksProcessed: 0,
            filesDeleted: 0,
            storageFreed: 0,
            dryRun: dryRun
          }
        };
      }

      console.log(`找到对应的userId: ${targetUserId}`);
    }

    // ==================== 查询需要清理的任务 ====================
    console.log('查询需要清理的任务...');

    const tasksToCleanup = await findTasksToCleanup(tasksCollection, cutoffDate, batchSize, targetUserId);
    
    if (tasksToCleanup.length === 0) {
      const message = openId ? `没有找到用户 ${openId} 需要清理的任务` : '没有找到需要清理的任务';
      console.log(message);
      return {
        code: 200,
        message: '没有需要清理的数据',
        data: {
          cleanupDate: cutoffDate.toISOString(),
          tasksProcessed: 0,
          filesDeleted: 0,
          storageFreed: 0,
          dryRun: dryRun,
          targetOpenId: openId
        }
      };
    }

    const logMessage = openId ?
      `找到用户 ${openId} 的 ${tasksToCleanup.length} 个需要清理的任务` :
      `找到 ${tasksToCleanup.length} 个需要清理的任务`;
    console.log(logMessage);

    // ==================== 试运行模式 ====================
    if (dryRun) {
      const summary = await generateCleanupSummary(tasksToCleanup);
      console.log('试运行模式，清理预览：', summary);
      
      return {
        code: 200,
        message: '试运行完成',
        data: {
          ...summary,
          cleanupDate: cutoffDate.toISOString(),
          dryRun: true,
          targetOpenId: openId,
          targetUserId: targetUserId
        }
      };
    }

    // ==================== 执行清理操作 ====================
    console.log('开始执行清理操作...');
    
    const cleanupResult = await performCleanup(
      tasksToCleanup, 
      tasksCollection, 
      skipOssCleanup
    );

    // ==================== 记录审计日志 ====================
    await recordAuditLog(db, {
      operation: 'cleanup_old_data',
      cutoffDate: cutoffDate,
      result: cleanupResult,
      clientIP: CLIENTIP,
      targetOpenId: openId,
      targetUserId: targetUserId,
      timestamp: new Date()
    });

    console.log('数据清理完成，结果：', cleanupResult);

    return {
      code: 200,
      message: '数据清理完成',
      data: {
        ...cleanupResult,
        cleanupDate: cutoffDate.toISOString(),
        dryRun: false,
        targetOpenId: openId,
        targetUserId: targetUserId
      }
    };

  } catch (error) {
    console.error('cleanup-old-data 云函数执行错误：', error);
    
    // 记录错误日志
    try {
      const db = uniCloud.database();
      await recordAuditLog(db, {
        operation: 'cleanup_old_data',
        error: error.message,
        targetOpenId: event.openId || null,
        timestamp: new Date()
      });
    } catch (logError) {
      console.error('记录错误日志失败：', logError);
    }
    
    return {
      code: 500,
      message: '数据清理失败',
      error: error.message
    };
  }
};

/**
 * 通过openId查找对应的userId
 */
async function getUserIdByOpenId(db, openId) {
  try {
    console.log(`查询openId ${openId} 对应的用户信息...`);

    // 查询用户表，通过openId找到userId
    const userCollection = db.collection('users');
    const result = await userCollection
      .where({
		  openid:openId
      })
      .field({
        _id: true
      })
      .limit(1)
      .get();

    if (result.data && result.data.length > 0) {
      const userId = result.data[0]._id;
      console.log(`找到用户ID: ${userId}`);
      return userId;
    }

    console.log(`未找到openId ${openId} 对应的用户`);
    return null;
  } catch (error) {
    console.error('通过openId查找userId失败：', error);
    throw error;
  }
}

/**
 * 查询需要清理的任务
 */
async function findTasksToCleanup(tasksCollection, cutoffDate, batchSize, userId = null) {
  try {
    // 查询创建时间早于截止日期的任务
    // 包括所有状态的任务，但排除已经标记为deleted的任务（避免重复处理）
    const db = uniCloud.database();

    // 构建查询条件
    const whereCondition = {
      createTime: db.command.lt(cutoffDate),
      status: db.command.neq('deleted') // 排除已删除的任务
    };

    // 如果指定了userId，则只查询该用户的任务
    if (userId) {
      whereCondition.userId = userId;
      console.log(`查询用户 ${userId} 的任务，截止时间：${cutoffDate.toISOString()}`);
    }

    const result = await tasksCollection
      .where(whereCondition)
      .field({
        _id: true,
        userId: true,
        fileName: true,
        fileSize: true,
        status: true,
        ossUrl: true,
        audioOssUrl: true,
        subtitleOssUrl: true,
        finalVideoUrl: true,
        createTime: true
      })
      .orderBy('createTime', 'asc') // 按创建时间升序，优先清理最旧的数据
      .limit(batchSize)
      .get();

    const tasks = result.data || [];
    console.log(`查询到 ${tasks.length} 个符合条件的任务`);

    return tasks;
  } catch (error) {
    console.error('查询需要清理的任务失败：', error);
    throw error;
  }
}

/**
 * 生成清理预览摘要（试运行模式）
 */
async function generateCleanupSummary(tasks) {
  let totalFileSize = 0;
  let fileCount = 0;
  const statusCounts = {};

  tasks.forEach(task => {
    // 统计文件大小
    if (task.fileSize) {
      totalFileSize += task.fileSize;
    }

    // 统计文件数量
    if (task.ossUrl) fileCount++;
    if (task.audioOssUrl) fileCount++;
    if (task.subtitleOssUrl) fileCount++;
    if (task.finalVideoUrl) fileCount++;

    // 统计任务状态
    statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
  });

  return {
    tasksToDelete: tasks.length,
    filesToDelete: fileCount,
    estimatedStorageFreed: totalFileSize,
    tasksByStatus: statusCounts,
    oldestTaskDate: tasks.length > 0 ? tasks[0].createTime : null
  };
}

/**
 * 执行清理操作
 */
async function performCleanup(tasks, tasksCollection, skipOssCleanup) {
  const result = {
    tasksProcessed: 0,
    tasksDeleted: 0,
    filesDeleted: 0,
    storageFreed: 0,
    errors: []
  };

  // 初始化OSS客户端（如果需要清理OSS文件）
  let ossClient = null;
  if (!skipOssCleanup) {
    try {
      ossClient = await initializeOSSClient();
    } catch (error) {
      console.error('初始化OSS客户端失败：', error);
      result.errors.push(`OSS客户端初始化失败: ${error.message}`);
      // 如果OSS初始化失败，跳过OSS清理但继续数据库清理
    }
  }

  // 批量处理任务
  for (const task of tasks) {
    try {
      result.tasksProcessed++;
      
      // 清理OSS文件
      if (ossClient && !skipOssCleanup) {
        const ossResult = await cleanupTaskOSSFiles(task, ossClient);
        result.filesDeleted += ossResult.filesDeleted;
        result.storageFreed += ossResult.storageFreed;
        
        if (ossResult.errors.length > 0) {
          result.errors.push(...ossResult.errors);
        }
      }

      // 物理删除数据库记录
      await tasksCollection.doc(task._id).remove();
      result.tasksDeleted++;
      
      console.log(`任务 ${task._id} 清理完成`);
      
    } catch (error) {
      console.error(`清理任务 ${task._id} 失败：`, error);
      result.errors.push(`任务 ${task._id} 清理失败: ${error.message}`);
    }
  }

  return result;
}

/**
 * 初始化OSS客户端
 */
async function initializeOSSClient() {
  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    console.log('OSS客户端初始化成功');
    return client;
  } catch (error) {
    console.error('OSS客户端初始化失败：', error);
    throw error;
  }
}

/**
 * 清理任务相关的OSS文件
 */
async function cleanupTaskOSSFiles(task, ossClient) {
  const result = {
    filesDeleted: 0,
    storageFreed: 0,
    errors: []
  };

  // 需要清理的文件URL列表
  const fileUrls = [
    { url: task.ossUrl, type: 'original_video' },
    { url: task.audioOssUrl, type: 'audio' },
    { url: task.subtitleOssUrl, type: 'subtitle' },
    { url: task.finalVideoUrl, type: 'final_video' }
  ].filter(item => item.url && item.url.trim() !== '');

  console.log(`任务 ${task._id} 需要清理 ${fileUrls.length} 个文件`);

  for (const fileInfo of fileUrls) {
    try {
      const objectKey = extractObjectKeyFromUrl(fileInfo.url);
      if (!objectKey) {
        result.errors.push(`无法解析文件路径: ${fileInfo.url}`);
        continue;
      }

      // 获取文件信息（用于计算释放的存储空间）
      let fileSize = 0;
      try {
        const headResult = await ossClient.head(objectKey);
        fileSize = parseInt(headResult.res.headers['content-length'] || '0');
      } catch (headError) {
        // 文件可能已经不存在，继续删除操作
        console.warn(`获取文件信息失败，文件可能已不存在: ${objectKey}`);
      }

      // 删除文件
      await ossClient.delete(objectKey);
      result.filesDeleted++;
      result.storageFreed += fileSize;

      console.log(`删除OSS文件成功: ${objectKey} (${fileSize} bytes)`);

    } catch (error) {
      console.error(`删除OSS文件失败: ${fileInfo.url}`, error);
      result.errors.push(`删除${fileInfo.type}文件失败: ${error.message}`);
    }
  }

  return result;
}

/**
 * 从OSS URL中提取objectKey
 */
function extractObjectKeyFromUrl(ossUrl) {
  try {
    if (!ossUrl || typeof ossUrl !== 'string') {
      return null;
    }

    // 匹配OSS URL格式：https://bucket.region.aliyuncs.com/objectKey
    const match = ossUrl.match(/https:\/\/[^.]+\.[^.]+\.aliyuncs\.com\/(.+)/);
    return match ? decodeURIComponent(match[1]) : null;
  } catch (error) {
    console.error("解析OSS URL失败：", error);
    return null;
  }
}

/**
 * 记录审计日志
 */
async function recordAuditLog(db, logData) {
  try {
    const auditCollection = db.collection('audit_logs');

    const logEntry = {
      operation: logData.operation,
      operationTime: logData.timestamp || new Date(),
      details: {
        cutoffDate: logData.cutoffDate,
        result: logData.result,
        error: logData.error,
        targetOpenId: logData.targetOpenId, // 记录目标openId
        targetUserId: logData.targetUserId  // 记录目标userId
      },
      clientIP: logData.clientIP,
      createTime: new Date()
    };

    await auditCollection.add(logEntry);
    console.log('审计日志记录成功');
  } catch (error) {
    console.error('记录审计日志失败：', error);
    // 审计日志失败不应该影响主要操作
  }
}
